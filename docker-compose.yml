services:
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    command: --character-set-server=utf8 --collation-server=utf8_general_ci
    ports:
      - 3306:3306
    environment:
      MYSQL_ROOT_PASSWORD: rd123456
      MYSQL_ROOT_HOST: '%'
  redis:
    image: redis:7-alpine
    platform: linux/amd64
    ports:
      - 6379:6379
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    platform: linux/amd64
    ports:
      - 5672:5672
      - 15672:15672
    environment:
      RABBITMQ_DEFAULT_USER: agent
      RABBITMQ_DEFAULT_PASS: d42agent
  kong-database:
    image: postgres:15-alpine
    platform: linux/amd64
    restart: always
    networks:
      - kong-net
    environment:
      POSTGRES_USER: root
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: 123456
      TZ: Asia/Shanghai
    ports:
      - "5432:5432"

  # 为kong数据库初始化
  kong-migration:
    image: kong:3.4
    platform: linux/amd64
    command: "kong migrations bootstrap"
    networks:
      - kong-net
    restart: on-failure
    environment:
      KONG_PG_HOST: kong-database
      KONG_DATABASE: postgres
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
    depends_on:
      - kong-database

  # 启动kong
  kong:
    image: kong:3.4
    platform: linux/amd64
    restart: always
    networks:
      - kong-net
    environment:
      KONG_PG_HOST: kong-database
      KONG_DATABASE: postgres
      KONG_PG_USER: root
      KONG_PG_PASSWORD: 123456
      KONG_CASSANDRA_CONTACT_POINTS: kong-database
      KONG_PROXY_LISTEN: 0.0.0.0:8000
      KONG_PROXY_LISTEN_SSL: 0.0.0.0:8443
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      TZ: Asia/Shanghai
    depends_on:
      - kong-migration
      - kong-database
    ports:
      - "5001:8001"
      - "5080:8000"
      - "8443:8443"
      - "8444:8444"
  manager:
    build:
      context: ./d42paas_manager
      dockerfile: Dockerfile
    platform: linux/amd64
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=devcontainer
      - SPRING_DATASOURCE_URL=***********************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=rd123456
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=agent
      - SPRING_RABBITMQ_PASSWORD=d42agent
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - kong-net
#  dockerImage:
#    build: ./docker/dockerImage
#    image: cr.1024paas.com/app/docker
#  dockerMysqlImage:
#    build: ./docker/mysql
#    image: cr.1024paas.com/app/mysql
networks:
  kong-net:
    driver: bridge