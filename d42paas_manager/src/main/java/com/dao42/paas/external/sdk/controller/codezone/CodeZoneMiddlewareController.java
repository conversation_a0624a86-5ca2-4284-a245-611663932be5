package com.dao42.paas.external.sdk.controller.codezone;

import com.dao42.paas.bean.CurrentUserBean;
import com.dao42.paas.common.constants.MiddlewareEnvConstants;
import com.dao42.paas.enums.DockerStatus;
import com.dao42.paas.enums.MiddlewareStatusEnum;
import com.dao42.paas.enums.PlaygroundBindType;
import com.dao42.paas.exception.docker.DockerCreateException;
import com.dao42.paas.exception.docker.DockerStartException;
import com.dao42.paas.exception.resource.NoDockerServerException;
import com.dao42.paas.exception.resource.ResourceNotEnoughException;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneCreateDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneMiddlewareSwitchDTO;
import com.dao42.paas.external.sdk.dto.codezone.CodeZoneMiddlewareVersionSwitchDTO;
import com.dao42.paas.external.sdk.dto.middleware.MiddlewareDTO;
import com.dao42.paas.external.sdk.dto.middleware.MiddlewareHealthDTO;
import com.dao42.paas.framework.currentUser.CurrentUser;
import com.dao42.paas.framework.dto.result.ListResultDTO;
import com.dao42.paas.framework.dto.result.ResultDTO;
import com.dao42.paas.framework.exceptions.CustomRuntimeException;
import com.dao42.paas.model.Playground;
import com.dao42.paas.model.codezone.CodeZone;
import com.dao42.paas.model.docker.DockerContainer;
import com.dao42.paas.model.middleware.MiddlewareConfig;
import com.dao42.paas.model.middleware.MiddlewareDefine;
import com.dao42.paas.model.middleware.MiddlewareInstance;
import com.dao42.paas.repository.PlaygroundRepository;
import com.dao42.paas.repository.middleware.MiddlewareConfigRepository;
import com.dao42.paas.repository.middleware.MiddlewareDefineRepository;
import com.dao42.paas.service.RedisExternalService;
import com.dao42.paas.service.codezone.CodeZoneMiddlewareService;
import com.dao42.paas.service.codezone.CodeZoneService;
import com.dao42.paas.utils.JsonUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSort;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RequiredArgsConstructor
@RestController
@RequestMapping("/sdk/codeZones/{codeZoneId}/middlewareDefines")
@Slf4j
@Api(tags = "codeZoneMiddleware")
@ApiSort(value = 10)
public class CodeZoneMiddlewareController {

    private final MiddlewareDefineRepository middlewareDefineRepository;

    private final MiddlewareConfigRepository middlewareConfigRepository;

    private final CodeZoneMiddlewareService codeZoneMiddlewareService;

    private final CodeZoneService codeZoneService;

    private final PlaygroundRepository playgroundRepository;

    private final RedisExternalService redisExternalService;

    @GetMapping
    @ApiOperation(value = "获取codeZone依赖的中间件列表")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<MiddlewareDTO> list(@CurrentUser @ApiIgnore CurrentUserBean tenant,
        @PathVariable Long codeZoneId) {

        CodeZone codeZone = codeZoneService.getCodeZone(codeZoneId);
        DockerContainer dockerContainer = codeZoneService.getDockerByCodeZone(codeZone);

        List<MiddlewareDTO> dtoList = new ArrayList<>();
        if (dockerContainer == null) {
            for (MiddlewareConfig config : codeZone.getMiddlewareConfigList()) {
                MiddlewareDTO dto = new MiddlewareDTO();
                dto.setMiddlewareDefineId(config.getDefine().getId());
                dto.setName(config.getDefine().getName());
                dto.setCode(config.getDefine().getCode());
                dto.setExternalSwitch(config.getExternalSwitch());
                Map<String, Object> userEnvMap = JsonUtil.jsonToMap(config.getUserEnv());
                if (userEnvMap != null) {
                    dto.setMiddlewareConfigId(String.valueOf(config.getId()));
                    String middleType = config.getDefine().getCode();
                    dto.setExternalEnvMap(middleType, userEnvMap);
                    dto.setInnerEnvMap(middleType, userEnvMap, false);
                    dto.setCEnvMap(middleType, userEnvMap);
                }
                dtoList.add(dto);
            }
        } else {
            for (MiddlewareInstance instance : dockerContainer.getMiddlewares()) {
                if (instance.getStatus().equals(DockerStatus.DELETE_SUCCESS)) {
                    continue;
                }
                MiddlewareDTO dto = new MiddlewareDTO();
                dto.setExternalSwitch(instance.getConfig().getExternalSwitch());
                dto.setMiddlewareDefineId(instance.getConfig().getDefine().getId());
                dto.setName(instance.getConfig().getDefine().getName());
                dto.setCode(instance.getConfig().getDefine().getCode());
                Map<String, Object> userEnvMap = JsonUtil.jsonToMap(instance.getUserEnv());
                if (userEnvMap != null) {
                    dto.setMiddlewareConfigId(String.valueOf(instance.getConfig().getId()));
                    String middleType = instance.getConfig().getDefine().getCode();
                    dto.setCEnvMap(middleType, userEnvMap);
                    dto.setExternalEnvMap(middleType, userEnvMap);
                    dto.setInnerEnvMap(middleType, userEnvMap, false);
                }
                dtoList.add(dto);
            }
        }

        return ListResultDTO.success(dtoList);
    }

    @GetMapping("/config")
    @ApiOperation(value = "ai agent获取codeZone依赖的中间件列表")
    @ApiOperationSupport(order = 1)
    public ListResultDTO<MiddlewareDTO> config(@CurrentUser @ApiIgnore CurrentUserBean tenant,
        @PathVariable Long codeZoneId) {

        CodeZone codeZone = codeZoneService.getCodeZone(codeZoneId);
        DockerContainer dockerContainer = codeZoneService.getDockerByCodeZone(codeZone);

        List<MiddlewareDTO> dtoList = new ArrayList<>();
        if (dockerContainer == null) {
            for (MiddlewareConfig config : codeZone.getMiddlewareConfigList()) {
                MiddlewareDTO dto = new MiddlewareDTO();
                dto.setMiddlewareDefineId(config.getDefine().getId());
                dto.setName(config.getDefine().getName());
                dto.setCode(config.getDefine().getCode());
                dto.setExternalSwitch(config.getExternalSwitch());
                Map<String, Object> userEnvMap = JsonUtil.jsonToMap(config.getUserEnv());
                if (userEnvMap != null) {
                    dto.setMiddlewareConfigId(String.valueOf(config.getId()));
                    String middleType = config.getDefine().getCode();
                    dto.setInnerEnvMap(middleType, userEnvMap, true);
                }
                dtoList.add(dto);
            }
        } else {
            for (MiddlewareInstance instance : dockerContainer.getMiddlewares()) {
                if (instance.getStatus().equals(DockerStatus.DELETE_SUCCESS)) {
                    continue;
                }
                MiddlewareDTO dto = new MiddlewareDTO();
                dto.setExternalSwitch(instance.getConfig().getExternalSwitch());
                dto.setMiddlewareDefineId(instance.getConfig().getDefine().getId());
                dto.setName(instance.getConfig().getDefine().getName());
                dto.setCode(instance.getConfig().getDefine().getCode());
                Map<String, Object> userEnvMap = JsonUtil.jsonToMap(instance.getUserEnv());
                if (userEnvMap != null) {
                    dto.setMiddlewareConfigId(String.valueOf(instance.getConfig().getId()));
                    String middleType = instance.getConfig().getDefine().getCode();
                    dto.setInnerEnvMap(middleType, userEnvMap, true);
                }
                dtoList.add(dto);
            }
        }

        return ListResultDTO.success(dtoList);
    }

    @PostMapping("/{middlewareDefineId}")
    @ApiOperation(value = "增加codeZone依赖的中间件")
    @ApiOperationSupport(order = 2)
    public ResultDTO<MiddlewareDTO> add(@CurrentUser @ApiIgnore CurrentUserBean tenant, @PathVariable Long codeZoneId,
        @PathVariable Long middlewareDefineId)
        throws DockerCreateException, ResourceNotEnoughException, NoDockerServerException, DockerStartException {
        MiddlewareDTO dto = addMiddleware(codeZoneId, middlewareDefineId, null, "add version");
        return ResultDTO.success(dto);
    }

    // 增加新的中间件
    private MiddlewareDTO addMiddleware(Long codeZoneId, Long middlewareDefineId, Long oldVersionConfigId, String switchVersion)
        throws DockerCreateException, ResourceNotEnoughException, NoDockerServerException, DockerStartException {
        try {
            MiddlewareDefine define = middlewareDefineRepository.findById(middlewareDefineId)
                .orElseThrow(() -> new CustomRuntimeException("define.not.existed", "middleware defineId not existed"));

            // 处理codeZone已经存在的容器
            Playground playground =
                playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE, codeZoneId);
            if (playground == null) {
                throw new CustomRuntimeException("playground is not found", "Please apply for a playground first");
            }

            CodeZone codeZone = this.codeZoneService.getCodeZone(codeZoneId);
            for (MiddlewareConfig config : codeZone.getMiddlewareConfigList()) {
                if (config.getDefine().equals(define)) {
                    throw new CustomRuntimeException("middleware is exist", "Middleware of the same type already exists");
                }
            }
            MiddlewareInstance instance = codeZoneMiddlewareService.addInstance(codeZone, define, playground, oldVersionConfigId, switchVersion);

            MiddlewareDTO dto = new MiddlewareDTO();
            dto.setMiddlewareDefineId(instance.getConfig().getDefine().getId());
            dto.setName(instance.getConfig().getDefine().getName());
            Map<String, Object> userEnvMap = JsonUtil.jsonToMap(instance.getUserEnv());
            if (userEnvMap != null) {
                dto.getEnvMap().putAll(userEnvMap);
            }

            return dto;
        } catch (Exception e) {
            log.info("codezone-middleware-add, exception: ", e);
            throw e;
        }
    }

    @GetMapping("/{middlewareDefineId}/searchStatus")
    @ApiOperation(value = "查询中间件运行状态")
    @ApiOperationSupport(order = 2)
    public ResultDTO<MiddlewareHealthDTO> searchStatus(@CurrentUser @ApiIgnore CurrentUserBean tenant,
        @PathVariable Long codeZoneId,
        @PathVariable Long middlewareDefineId) {
        MiddlewareHealthDTO middlewareHealthDTO = new MiddlewareHealthDTO();
        MiddlewareDefine define = middlewareDefineRepository.findById(middlewareDefineId)
            .orElseThrow(() -> new CustomRuntimeException("define.not.existed", "middleware defineId not existed"));
        Playground playground = this.playgroundRepository.findByBindTypeAndBindObject(PlaygroundBindType.CODE_ZONE,
            codeZoneId
        );
        if(playground == null) {
            throw new CustomRuntimeException("The playground does not exist; please create a playground first");
        }
        playground.getDockerContainer().getMiddlewares().forEach(item -> {
            if (define.equals(item.getConfig().getDefine())) {
                String result = redisExternalService.getMiddlewareHealth(item.getContainerId());
                middlewareHealthDTO.setMiddlewareStatusEnum(
                    result == null ? MiddlewareStatusEnum.STOP : MiddlewareStatusEnum.valueOf(result));
            }
        });
        return ResultDTO.success(middlewareHealthDTO);
    }

    @DeleteMapping("/{middlewareDefineId}")
    @ApiOperation(value = "删除codeZone依赖的中间件")
    @ApiOperationSupport(order = 3)
    public ResultDTO<Void> remove(@CurrentUser @ApiIgnore CurrentUserBean tenant, @PathVariable Long codeZoneId,
        @PathVariable Long middlewareDefineId) {
        removeMiddleware(codeZoneId, middlewareDefineId, "user remove");
        return ResultDTO.success();
    }

    // 删除中间件
    private void removeMiddleware(Long codeZoneId, Long middlewareDefineId, String switchVersion) {
        CodeZone codeZone = this.codeZoneService.getCodeZone(codeZoneId);
        MiddlewareDefine define = middlewareDefineRepository.findById(middlewareDefineId)
            .orElseThrow(() -> new CustomRuntimeException("define.not.existed", "middleware defineId not existed"));
        codeZoneMiddlewareService.removeBind(codeZone, define, switchVersion);
    }

    @PostMapping("/{middlewareConfigId}/switch")
    @ApiOperation(value = "开启或关闭中间件外网访问")
    @ApiOperationSupport(order = 4)
    public ResultDTO<Void> externalSwitch(@CurrentUser @ApiIgnore CurrentUserBean tenant, @PathVariable String codeZoneId,
        @PathVariable Long middlewareConfigId, @RequestBody CodeZoneMiddlewareSwitchDTO dto) {
        MiddlewareConfig middleConfig = middlewareConfigRepository.findById(middlewareConfigId)
            .orElseThrow(() -> new CustomRuntimeException("config.not.existed", "middleware configId not existed"));
        if (middleConfig != null) {
            middleConfig.setExternalSwitch(dto.getExternalSwitch());
            middlewareConfigRepository.save(middleConfig);
        }

        return ResultDTO.success();
    }

    @PostMapping("/version/switch")
    @ApiOperation(value = "切换中间件版本")
    @ApiOperationSupport(order = 4)
    public ResultDTO<MiddlewareDTO> middlewareVersionSwitch(@CurrentUser @ApiIgnore CurrentUserBean tenant,
        @PathVariable Long codeZoneId, @RequestBody CodeZoneMiddlewareVersionSwitchDTO dto)
        throws DockerCreateException, ResourceNotEnoughException, NoDockerServerException, DockerStartException {
        try {
            Long oldVersionConfigId = dto.getOldVersionMiddlewareConfigId();
            Long oldVersionDefineId = dto.getOldVersionMiddlewareDefineId();
            Long newVersionDefineId = dto.getNewVersionMiddlewareDefineId();

            if (Objects.equals(oldVersionDefineId, newVersionDefineId)) {
                throw new CustomRuntimeException("The middleware version equals");
            }

            MiddlewareDefine oldMiddleDefine = middlewareDefineRepository.findById(oldVersionDefineId)
                .orElseThrow(() -> new CustomRuntimeException("define.not.existed", "middleware oldDefineId not existed"));
            MiddlewareDefine newMiddleDefine = middlewareDefineRepository.findById(newVersionDefineId)
                .orElseThrow(
                    () -> new CustomRuntimeException("config.not.existed", "middleware newConfigId not existed"));

            if (oldMiddleDefine == null || newMiddleDefine == null) {
                throw new CustomRuntimeException("The middleware does not exist");
            }

            MiddlewareConfig middlewareConfig = middlewareConfigRepository.findById(oldVersionConfigId)
                .orElseThrow(() -> new CustomRuntimeException("define.not.existed", "middleware configId not existed"));
            if (middlewareConfig == null) {
                throw new CustomRuntimeException("The middleware config does not exist");
            }

            // 删除旧的中间件
            log.info("codezone-middleware-middlewareVersionSwitch, codeZoneId: {}, dto: {}", codeZoneId, dto);
            removeMiddleware(codeZoneId, oldVersionDefineId, "user switch version");
            log.info("codezone-middleware-middlewareVersionSwitch, codeZoneId: {}, dto: {}", codeZoneId, dto);

            // 安装新版本中间件
            MiddlewareDTO dto1 = addMiddleware(codeZoneId, newVersionDefineId, oldVersionConfigId, "user switch version");
            return ResultDTO.success(dto1);
        } catch (Exception e) {
            log.info("codezone-middleware-middlewareVersionSwitch, exception: ", e);
            throw e;
        }
    }
}
