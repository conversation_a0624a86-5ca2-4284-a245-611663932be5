package com.dao42.paas.external.paas.dto;

import cn.hutool.json.JSONUtil;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.lang.Nullable;

/**
 * 缩扩容
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AutoScalingDTO {

    /**
     * 是否启用伸缩组
     */
    @NotNull(groups = ModifyGroupState.class)
    private Boolean enable;

    /**
     * 期望实例数（包括手动加入的实例和自动添加的实例，最小实例数 ≦ 期望实例数 ≦ 最大实例数）
     */
    @NotNull(groups = ModifyDesiredCapacity.class)
    public Integer desiredCapacity;

    /**
     * 手动扩容时的人数
     */
    @NotNull(groups = ModifyDesiredCapacityByNumberPeople.class)
    private Integer numberPeople;

    /**
     * 最小实例数
     */
    @Nullable
    public Integer minSize;

    /**
     * 最大实例数
     */
    @Nullable
    public Integer maxSize;

    /**
     * 启用/停用 伸缩组
     */
    public @interface ModifyGroupState {

    }

    /**
     * 修改期望实例数
     */
    public @interface ModifyDesiredCapacity {

    }

    /**
     * 修改期望实例数根据人数
     */
    public @interface ModifyDesiredCapacityByNumberPeople {

    }
    @Override
    public String toString() {
        return JSONUtil.toJsonStr(this);
    }
}
