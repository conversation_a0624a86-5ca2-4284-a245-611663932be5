FROM eclipse-temurin:17-jdk AS builder
WORKDIR /build
COPY . .
RUN apt-get update && \
    apt-get install -y --no-install-recommends maven && \
    mvn clean package -DskipTests && \
    rm -rf /var/lib/apt/lists/*

FROM eclipse-temurin:17-jdk
# set timezone
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
# apt-get update
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    tini curl openssh-client && \
    groupadd -g 2000 runner && \
    useradd -m -u 2000 -g runner -s /bin/bash runner && \
    rm -rf /var/lib/apt/lists/*

# copy arthas
COPY --from=hengyunabc/arthas:latest /opt/arthas /opt/arthas
ADD https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar /app/opentelemetry-javaagent.jar
RUN chmod +r /app/opentelemetry-javaagent.jar

# copy jar from builder stage
COPY --from=builder /build/d42paas_manager/target/manager.jar /app/d42paas-manager.jar
WORKDIR /app
RUN chown -R runner:runner /app
ENTRYPOINT ["/usr/bin/tini", "--"]

# 设置启动用户为 runner
USER runner

# 开发环境使用 sleep infinity 命令
CMD ["java", "-jar", "-Duser.timezone=Asia/Shanghai", "d42paas-manager.jar"]