{"name": "D42PaaS Backend Development", "dockerComposeFile": ["../docker-compose.yml", "docker-compose.dev.yml"], "service": "manager", "workspaceFolder": "/workspace", "customizations": {"vscode": {"extensions": ["vscjava.vscode-java-pack", "redhat.java", "vscjava.vscode-spring-boot-dashboard", "vscjava.vscode-maven", "vscjava.vscode-java-debug", "vscjava.vscode-java-test"], "settings": {"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.format.settings.url": ".vscode/java-formatter.xml", "java.format.settings.profile": "GoogleStyle"}}}, "forwardPorts": [8080, 3306, 6379, 5672, 15672, 5080, 5001], "postCreateCommand": "mvn clean install -DskipTests", "remoteUser": "runner"}