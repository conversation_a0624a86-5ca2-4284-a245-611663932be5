services:
  manager:
    image: openjdk:17-jdk-slim
    volumes:
      - ..:/workspace:cached
      - ~/.m2:/home/<USER>/.m2:cached
    command: sleep infinity
    networks:
      - kong-net
    environment:
      - JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
      - MAVEN_HOME=/usr/share/maven
      - PATH=/usr/lib/jvm/java-17-openjdk-amd64/bin:/usr/share/maven/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
      - SPRING_PROFILES_ACTIVE=devcontainer
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=agent
      - SPRING_RABBITMQ_PASSWORD=d42agent
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=rd123456
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - kong

