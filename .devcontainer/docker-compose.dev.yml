version: '3'
services:
  manager:
    image: mcr.microsoft.com/vscode/devcontainers/java:17
    volumes:
      - ..:/workspace:cached
      - ~/.m2:/home/<USER>/.m2:cached
    command: sleep infinity
    networks:
      - kong-net
    environment:
      - JAVA_HOME=/usr/lib/jvm/java-17-openjdk-arm64
      - MAVEN_HOME=/usr/share/maven
      - PATH=/usr/lib/jvm/java-17-openjdk-arm64/bin:/usr/share/maven/bin:$PATH
      - SPRING_PROFILES_ACTIVE=devcontainer
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_RABBITMQ_HOST=rabbitmq
      - SPRING_RABBITMQ_PORT=5672
      - SPRING_RABBITMQ_USERNAME=agent
      - SPRING_RABBITMQ_PASSWORD=d42agent
      - SPRING_DATASOURCE_URL=********************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=root
      - SPRING_DATASOURCE_PASSWORD=rd123456
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - kong

networks:
  kong-net:
    external: true 